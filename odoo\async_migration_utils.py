# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Migration utilities to help transition from synchronous psycopg2 to async asyncpg.
These utilities provide compatibility layers and migration helpers.
"""
from __future__ import annotations

import asyncio
import functools
import inspect
import logging
from typing import Any, Callable, TypeVar, Union

_logger = logging.getLogger(__name__)

F = TypeVar('F', bound=Callable[..., Any])

def async_to_sync(async_func: Callable[..., Any]) -> Callable[..., Any]:
    """
    Convert an async function to a synchronous one by running it in the event loop.
    This is useful for gradual migration from sync to async code.
    """
    @functools.wraps(async_func)
    def wrapper(*args, **kwargs):
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an async context, we need to use run_in_executor
                # to avoid "RuntimeError: cannot be called from a running event loop"
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, async_func(*args, **kwargs))
                    return future.result()
            else:
                return loop.run_until_complete(async_func(*args, **kwargs))
        except RuntimeError:
            # Fallback: create a new event loop
            return asyncio.run(async_func(*args, **kwargs))
    
    return wrapper

def sync_to_async(sync_func: Callable[..., Any]) -> Callable[..., Any]:
    """
    Convert a synchronous function to an async one by running it in a thread pool.
    """
    @functools.wraps(sync_func)
    async def wrapper(*args, **kwargs):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, lambda: sync_func(*args, **kwargs))
    
    return wrapper

def auto_async_compat(func: F) -> F:
    """
    Decorator that makes a function compatible with both sync and async contexts.
    If called from async context, it runs asynchronously. Otherwise, it runs synchronously.
    """
    if asyncio.iscoroutinefunction(func):
        # Function is already async, add sync compatibility
        sync_version = async_to_sync(func)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Check if we're in an async context
            try:
                loop = asyncio.get_running_loop()
                # We're in an async context, return the coroutine
                return func(*args, **kwargs)
            except RuntimeError:
                # We're not in an async context, run synchronously
                return sync_version(*args, **kwargs)
        
        # Add both sync and async versions as attributes
        wrapper.async_version = func
        wrapper.sync_version = sync_version
        return wrapper
    else:
        # Function is sync, add async compatibility
        async_version = sync_to_async(func)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Always run synchronously for sync functions
            return func(*args, **kwargs)
        
        # Add both versions as attributes
        wrapper.async_version = async_version
        wrapper.sync_version = func
        return wrapper

class AsyncCompatCursor:
    """
    Compatibility wrapper that provides both sync and async interfaces for database cursors.
    This allows gradual migration from psycopg2 to asyncpg.
    """
    
    def __init__(self, async_cursor):
        self._async_cursor = async_cursor
        self._sync_methods = {}
    
    def __getattr__(self, name):
        """
        Dynamically provide sync versions of async methods.
        """
        attr = getattr(self._async_cursor, name)
        
        if asyncio.iscoroutinefunction(attr):
            # Create a sync version of the async method
            if name not in self._sync_methods:
                self._sync_methods[name] = async_to_sync(attr)
            return self._sync_methods[name]
        else:
            return attr
    
    async def __aenter__(self):
        """Async context manager support."""
        return await self._async_cursor.__aenter__()
    
    async def __aexit__(self, exc_type, exc_value, traceback):
        """Async context manager support."""
        return await self._async_cursor.__aexit__(exc_type, exc_value, traceback)
    
    def __enter__(self):
        """Sync context manager support."""
        return async_to_sync(self._async_cursor.__aenter__)()
    
    def __exit__(self, exc_type, exc_value, traceback):
        """Sync context manager support."""
        return async_to_sync(self._async_cursor.__aexit__)(exc_type, exc_value, traceback)

class AsyncCompatConnection:
    """
    Compatibility wrapper for database connections that provides both sync and async interfaces.
    """
    
    def __init__(self, async_connection):
        self._async_connection = async_connection
    
    def cursor(self):
        """Return a compatibility cursor that works in both sync and async contexts."""
        async_cursor = self._async_connection.cursor()
        return AsyncCompatCursor(async_cursor)
    
    def __getattr__(self, name):
        """Delegate to the async connection."""
        return getattr(self._async_connection, name)

def migrate_db_connect(db_name, allow_uri=False, readonly=False, use_async=True):
    """
    Migration-friendly database connection function.
    
    Args:
        db_name: Database name
        allow_uri: Whether to allow URI connections
        readonly: Whether to use readonly connection
        use_async: Whether to use async connections (default: True)
    
    Returns:
        A connection that works in both sync and async contexts
    """
    if use_async:
        try:
            from odoo.async_sql_db import async_db_connect
            
            # Create async connection and wrap it for compatibility
            async def _get_async_connection():
                return await async_db_connect(db_name, allow_uri=allow_uri, readonly=readonly)
            
            # For sync usage, we need to run this in the event loop
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # We're in an async context, return the coroutine wrapped in a compatibility layer
                    async_conn = async_to_sync(_get_async_connection)()
                else:
                    async_conn = loop.run_until_complete(_get_async_connection())
                
                return AsyncCompatConnection(async_conn)
            except RuntimeError:
                # Fallback: create new event loop
                async_conn = asyncio.run(_get_async_connection())
                return AsyncCompatConnection(async_conn)
                
        except ImportError:
            _logger.warning("Async database module not available, falling back to sync")
            use_async = False
    
    if not use_async:
        # Fallback to synchronous connection
        from odoo.sql_db import db_connect
        return db_connect(db_name, allow_uri=allow_uri, readonly=readonly)

class MigrationHelper:
    """
    Helper class for migrating database operations from sync to async.
    """
    
    @staticmethod
    def wrap_cursor_methods(cursor_class):
        """
        Class decorator to automatically wrap cursor methods for async compatibility.
        """
        for attr_name in dir(cursor_class):
            if not attr_name.startswith('_'):
                attr = getattr(cursor_class, attr_name)
                if callable(attr) and not asyncio.iscoroutinefunction(attr):
                    # Wrap sync methods to be async-compatible
                    setattr(cursor_class, f"async_{attr_name}", sync_to_async(attr))
        
        return cursor_class
    
    @staticmethod
    def create_async_wrapper(sync_function):
        """
        Create an async wrapper for a synchronous database function.
        """
        return sync_to_async(sync_function)
    
    @staticmethod
    def batch_migrate_queries(queries, db_name, batch_size=100):
        """
        Migrate a batch of queries to use async execution.
        
        Args:
            queries: List of (query, params) tuples
            db_name: Database name
            batch_size: Number of queries to execute in each batch
        
        Returns:
            List of results
        """
        async def _execute_batch():
            from odoo.async_sql_db import async_db_connect
            
            connection = await async_db_connect(db_name)
            cursor = connection.cursor()
            
            results = []
            try:
                async with cursor:
                    for i in range(0, len(queries), batch_size):
                        batch = queries[i:i + batch_size]
                        batch_results = []
                        
                        for query, params in batch:
                            await cursor.execute(query, params)
                            result = await cursor.fetchall()
                            batch_results.append(result)
                        
                        results.extend(batch_results)
                        await cursor.commit()
            finally:
                await cursor.close()
            
            return results
        
        return async_to_sync(_execute_batch)()

# Compatibility aliases for common psycopg2 imports
try:
    import psycopg2
    
    # Create async-compatible versions of common psycopg2 exceptions
    class AsyncInterfaceError(psycopg2.InterfaceError):
        pass
    
    class AsyncDatabaseError(psycopg2.DatabaseError):
        pass
    
    class AsyncOperationalError(psycopg2.OperationalError):
        pass
    
except ImportError:
    # Define basic exception classes if psycopg2 is not available
    class AsyncInterfaceError(Exception):
        pass
    
    class AsyncDatabaseError(Exception):
        pass
    
    class AsyncOperationalError(Exception):
        pass
