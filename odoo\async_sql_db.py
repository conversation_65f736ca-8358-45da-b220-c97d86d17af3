# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async PostgreSQL connector using asyncpg for high-performance async database operations.
This module provides async equivalents to the synchronous database operations in sql_db.py
while maintaining API compatibility for seamless migration.
"""
from __future__ import annotations

import asyncio
import logging
import os
import re
import threading
import time
import typing
import uuid
from contextlib import asynccontextmanager
from datetime import datetime, timedelta, timezone
from inspect import currentframe

import asyncpg
from werkzeug import urls

import odoo
from odoo.tools import config

_logger = logging.getLogger(__name__)

# SQL query logging
sql_counter = 0

def real_time():
    """Return the current time in seconds since epoch."""
    return time.time()

class AsyncCallbacks:
    """Async version of the Callbacks class for managing async hooks."""
    
    def __init__(self):
        self._callbacks = []
    
    def add(self, callback):
        """Add an async callback function."""
        self._callbacks.append(callback)
    
    async def run(self):
        """Run all callbacks asynchronously."""
        for callback in self._callbacks:
            if asyncio.iscoroutinefunction(callback):
                await callback()
            else:
                # Run sync callback in thread pool
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, callback)
    
    def clear(self):
        """Clear all callbacks."""
        self._callbacks.clear()

class AsyncSavepoint:
    """Async savepoint context manager."""
    
    def __init__(self, cursor):
        self._cursor = cursor
        self._name = f"sp_{uuid.uuid4().hex}"
        self._closed = False
    
    async def __aenter__(self):
        await self._cursor.execute(f"SAVEPOINT {self._name}")
        return self
    
    async def __aexit__(self, exc_type, exc_value, traceback):
        if not self._closed:
            if exc_type is None:
                await self._cursor.execute(f"RELEASE SAVEPOINT {self._name}")
            else:
                await self._cursor.execute(f"ROLLBACK TO SAVEPOINT {self._name}")
            self._closed = True
    
    async def close(self, rollback=False):
        """Close the savepoint."""
        if not self._closed:
            if rollback:
                await self._cursor.execute(f"ROLLBACK TO SAVEPOINT {self._name}")
            else:
                await self._cursor.execute(f"RELEASE SAVEPOINT {self._name}")
            self._closed = True

class AsyncFlushingSavepoint(AsyncSavepoint):
    """Async savepoint that flushes before entering and clears on exit."""
    
    async def __aenter__(self):
        await self._cursor.flush()
        return await super().__aenter__()
    
    async def __aexit__(self, exc_type, exc_value, traceback):
        try:
            return await super().__aexit__(exc_type, exc_value, traceback)
        finally:
            if exc_type is None:
                await self._cursor.flush()
            else:
                self._cursor.clear()

class AsyncBaseCursor:
    """Base class for async cursors that manage pre/post commit hooks."""
    
    def __init__(self):
        self.precommit = AsyncCallbacks()
        self.postcommit = AsyncCallbacks()
        self.prerollback = AsyncCallbacks()
        self.postrollback = AsyncCallbacks()
        self.transaction = None
    
    async def flush(self):
        """Flush the current transaction, and run precommit hooks."""
        if self.transaction is not None:
            await self.transaction.flush()
        await self.precommit.run()
    
    def clear(self):
        """Clear the current transaction, and clear precommit hooks."""
        if self.transaction is not None:
            self.transaction.clear()
        self.precommit.clear()
    
    def reset(self):
        """Reset the current transaction."""
        if self.transaction is not None:
            self.transaction.reset()
    
    def savepoint(self, flush=True):
        """Context manager entering in a new savepoint."""
        if flush:
            return AsyncFlushingSavepoint(self)
        else:
            return AsyncSavepoint(self)
    
    async def __aenter__(self):
        """Using the cursor as an async context manager."""
        return self
    
    async def __aexit__(self, exc_type, exc_value, traceback):
        try:
            if exc_type is None:
                await self.commit()
        finally:
            await self.close()

class AsyncCursor(AsyncBaseCursor):
    """Async cursor that wraps asyncpg connections."""
    
    def __init__(self, pool, dbname, dsn):
        super().__init__()
        self.__pool = pool
        self.dbname = dbname
        self.__dsn = dsn
        self._cnx = None
        self._closed = False
        self._now = None
        self.sql_log_count = 0
    
    async def _ensure_connection(self):
        """Ensure we have an active connection."""
        if self._cnx is None or self._cnx.is_closed():
            self._cnx = await self.__pool.borrow(self.__dsn)
    
    async def execute(self, query, params=None, log_exceptions=True):
        """Execute a query asynchronously."""
        global sql_counter
        
        await self._ensure_connection()
        
        start = real_time()
        try:
            params = params or []
            # Convert psycopg2-style parameters to asyncpg format
            if params and isinstance(params, (list, tuple)):
                # Convert %s placeholders to $1, $2, etc.
                query_converted = query
                param_count = 0
                while '%s' in query_converted:
                    param_count += 1
                    query_converted = query_converted.replace('%s', f'${param_count}', 1)
                result = await self._cnx.execute(query_converted, *params)
            else:
                result = await self._cnx.execute(query)
        except Exception as e:
            if log_exceptions:
                _logger.error("bad query: %s\nERROR: %s", query, e)
            raise
        finally:
            delay = real_time() - start
            if _logger.isEnabledFor(logging.DEBUG):
                _logger.debug("[%.3f ms] query: %s", 1000 * delay, self._format(query, params))
        
        self.sql_log_count += 1
        sql_counter += 1
        
        current_thread = threading.current_thread()
        if hasattr(current_thread, 'query_count'):
            current_thread.query_count += 1
            current_thread.query_time += delay
        
        # Optional hooks for performance and tracing analysis
        for hook in getattr(current_thread, 'query_hooks', ()):
            hook(self, query, params, start, delay)
        
        return result
    
    async def fetch(self, query, params=None):
        """Fetch all results from a query."""
        await self._ensure_connection()
        
        if params and isinstance(params, (list, tuple)):
            # Convert %s placeholders to $1, $2, etc.
            query_converted = query
            param_count = 0
            while '%s' in query_converted:
                param_count += 1
                query_converted = query_converted.replace('%s', f'${param_count}', 1)
            return await self._cnx.fetch(query_converted, *params)
        else:
            return await self._cnx.fetch(query)
    
    async def fetchone(self, query=None, params=None):
        """Fetch one result from a query."""
        if query:
            results = await self.fetch(query, params)
            return results[0] if results else None
        else:
            # This is for compatibility with psycopg2 cursor.fetchone() after execute
            # We need to store the last result for this to work properly
            if hasattr(self, '_last_result') and self._last_result:
                return self._last_result.pop(0) if self._last_result else None
            return None

    async def fetchall(self, query=None, params=None):
        """Fetch all results from a query."""
        if query:
            return await self.fetch(query, params)
        else:
            # This is for compatibility with psycopg2 cursor.fetchall() after execute
            if hasattr(self, '_last_result'):
                result = self._last_result or []
                self._last_result = []
                return result
            return []

    async def fetchmany(self, size=None):
        """Fetch many results from the last query."""
        if hasattr(self, '_last_result') and self._last_result:
            if size is None:
                size = self.arraysize if hasattr(self, 'arraysize') else 1
            result = self._last_result[:size]
            self._last_result = self._last_result[size:]
            return result
        return []

    async def execute_values(self, query, argslist, template=None, page_size=100, fetch=False):
        """Execute a query with multiple parameter sets."""
        await self._ensure_connection()

        # Process in batches based on page_size
        results = []
        for i in range(0, len(argslist), page_size):
            batch = argslist[i:i + page_size]
            for args in batch:
                if template:
                    # Use template if provided (for compatibility with psycopg2.extras.execute_values)
                    formatted_query = template % args if isinstance(args, dict) else template
                    if fetch:
                        result = await self.fetch(formatted_query)
                        results.extend(result)
                    else:
                        await self.execute(formatted_query)
                else:
                    if fetch:
                        result = await self.fetch(query, args)
                        results.extend(result)
                    else:
                        await self.execute(query, args)

        return results if fetch else None

    def mogrify(self, query, params=None):
        """Return the query string after parameter substitution."""
        if params:
            try:
                return query % params
            except (TypeError, ValueError):
                return query
        return query

    @property
    def rowcount(self):
        """Number of rows affected by the last operation."""
        # asyncpg doesn't provide rowcount in the same way
        # This would need to be tracked separately
        return -1

    @property
    def description(self):
        """Description of the columns in the last query result."""
        # This would need to be implemented based on asyncpg's column info
        return None
    
    def _format(self, query, params):
        """Format query for logging."""
        if params:
            try:
                return query % params
            except (TypeError, ValueError):
                return f"{query} with params {params}"
        return query
    
    async def close(self, leak=False):
        """Close the cursor and return connection to pool."""
        if not self._closed:
            self._closed = True
            if self._cnx and not self._cnx.is_closed():
                if leak:
                    # Mark connection as leaked
                    pass
                else:
                    await self.__pool.give_back(self._cnx)
    
    async def commit(self):
        """Perform an SQL COMMIT."""
        await self.flush()
        if self._cnx and not self._cnx.is_closed():
            # For asyncpg, we need to manage transactions explicitly
            if hasattr(self, '_transaction') and self._transaction:
                await self._transaction.commit()
                self._transaction = None
        self.clear()
        self._now = None
        self.prerollback.clear()
        self.postrollback.clear()
        await self.postcommit.run()

    async def rollback(self):
        """Perform an SQL ROLLBACK."""
        self.clear()
        self.postcommit.clear()
        await self.prerollback.run()
        if self._cnx and not self._cnx.is_closed():
            # For asyncpg, we need to manage transactions explicitly
            if hasattr(self, '_transaction') and self._transaction:
                await self._transaction.rollback()
                self._transaction = None
        self._now = None
        await self.postrollback.run()

    async def _ensure_transaction(self):
        """Ensure we have an active transaction."""
        await self._ensure_connection()
        if not hasattr(self, '_transaction') or not self._transaction:
            self._transaction = self._cnx.transaction()
            await self._transaction.start()
    
    @property
    def closed(self):
        return self._closed or (self._cnx and self._cnx.is_closed())
    
    @property
    def readonly(self):
        return bool(self.__pool.readonly)
    
    async def now(self):
        """Return the transaction's timestamp."""
        if self._now is None:
            result = await self.fetch("SELECT (now() AT TIME ZONE 'UTC')")
            self._now = result[0][0] if result else datetime.now(timezone.utc)
        return self._now


class AsyncTestCursor(AsyncBaseCursor):
    """Async test cursor for testing environments."""

    def __init__(self, cursor, lock, readonly, current_test=None):
        assert isinstance(cursor, AsyncBaseCursor)
        self.current_test = current_test
        super().__init__()
        self._now = None
        self._closed = False
        self._cursor = cursor
        self.readonly = readonly
        self._lock = lock
        self._savepoint = None

    async def _ensure_savepoint(self):
        """Ensure we have a savepoint for test isolation."""
        if not self._savepoint:
            self._savepoint = self._cursor.savepoint(flush=False)
            await self._savepoint.__aenter__()

    async def execute(self, query, params=None, log_exceptions=True):
        """Execute a query in test mode."""
        await self._ensure_savepoint()
        return await self._cursor.execute(query, params, log_exceptions)

    async def fetch(self, query, params=None):
        """Fetch results in test mode."""
        await self._ensure_savepoint()
        return await self._cursor.fetch(query, params)

    async def fetchone(self, query=None, params=None):
        """Fetch one result in test mode."""
        await self._ensure_savepoint()
        return await self._cursor.fetchone(query, params)

    async def fetchall(self, query=None, params=None):
        """Fetch all results in test mode."""
        await self._ensure_savepoint()
        return await self._cursor.fetchall(query, params)

    async def close(self):
        """Close the test cursor."""
        if not self._closed:
            try:
                await self.rollback()
                if self._savepoint:
                    await self._savepoint.close(rollback=False)
            finally:
                self._closed = True

    async def commit(self):
        """Simulate commit in test mode."""
        await self.flush()
        if self._savepoint:
            await self._savepoint.close(rollback=self.readonly)
            self._savepoint = None
        self.clear()
        self.prerollback.clear()
        self.postrollback.clear()
        # TestCursor ignores post-commit hooks by default

    async def rollback(self):
        """Simulate rollback in test mode."""
        self.clear()
        self.postcommit.clear()
        await self.prerollback.run()
        if self._savepoint:
            await self._savepoint.close(rollback=True)
            self._savepoint = None
        await self.postrollback.run()

    def __getattr__(self, name):
        """Delegate to the underlying cursor."""
        return getattr(self._cursor, name)

    async def now(self):
        """Return the transaction's timestamp for tests."""
        if self._now is None:
            self._now = datetime.now(timezone.utc)
        return self._now


class AsyncConnectionPool:
    """Async connection pool for managing asyncpg connections."""

    def __init__(self, maxconn=64, readonly=False):
        self._pool = None
        self._maxconn = max(maxconn, 1)
        self._readonly = readonly
        self._lock = asyncio.Lock()
        self._connection_info = None

    def __repr__(self):
        pool_size = self._pool.get_size() if self._pool else 0
        idle_size = self._pool.get_idle_size() if self._pool else 0
        mode = 'read-only' if self._readonly else 'read/write'
        return f"AsyncConnectionPool({mode};idle={idle_size}/size={pool_size}/max={self._maxconn})"

    @property
    def readonly(self):
        return self._readonly

    def _debug(self, msg, *args):
        _logger.debug(('%r ' + msg), self, *args)

    async def _create_pool(self, connection_info):
        """Create the asyncpg connection pool."""
        if self._pool is None:
            try:
                # Convert connection info to asyncpg format
                dsn_parts = []
                if 'host' in connection_info:
                    dsn_parts.append(f"host={connection_info['host']}")
                if 'port' in connection_info:
                    dsn_parts.append(f"port={connection_info['port']}")
                if 'database' in connection_info:
                    dsn_parts.append(f"database={connection_info['database']}")
                if 'user' in connection_info:
                    dsn_parts.append(f"user={connection_info['user']}")
                if 'password' in connection_info:
                    dsn_parts.append(f"password={connection_info['password']}")

                dsn = " ".join(dsn_parts)

                self._pool = await asyncpg.create_pool(
                    dsn,
                    min_size=1,
                    max_size=self._maxconn,
                    command_timeout=60,
                    server_settings={
                        'application_name': 'odoo_async',
                        'jit': 'off',  # Disable JIT for better compatibility
                    }
                )
                self._connection_info = connection_info
                self._debug('Created async connection pool with max_size=%d', self._maxconn)
            except Exception as e:
                _logger.error('Failed to create async connection pool: %s', e)
                raise

    async def borrow(self, connection_info):
        """Borrow a connection from the pool."""
        async with self._lock:
            if self._pool is None:
                await self._create_pool(connection_info)

            try:
                connection = await self._pool.acquire()
                self._debug('Borrowed connection from pool')
                return connection
            except Exception as e:
                _logger.error('Failed to acquire connection from pool: %s', e)
                raise

    async def give_back(self, connection, keep_in_pool=True):
        """Return a connection to the pool."""
        if self._pool and connection:
            try:
                if keep_in_pool and not connection.is_closed():
                    await self._pool.release(connection)
                    self._debug('Returned connection to pool')
                else:
                    await connection.close()
                    self._debug('Closed connection (not returned to pool)')
            except Exception as e:
                _logger.error('Error returning connection to pool: %s', e)

    async def close_all(self):
        """Close all connections in the pool."""
        if self._pool:
            await self._pool.close()
            self._pool = None
            self._debug('Closed all connections in pool')


class AsyncConnection:
    """Async connection wrapper that provides cursor management."""

    def __init__(self, pool, dbname, dsn):
        self.__dbname = dbname
        self.__dsn = dsn
        self.__pool = pool

    @property
    def dsn(self):
        dsn = dict(self.__dsn)
        dsn.pop('password', None)
        return dsn

    @property
    def dbname(self):
        return self.__dbname

    def cursor(self):
        """Create a new async cursor."""
        _logger.debug('create async cursor to %r', self.dsn)
        return AsyncCursor(self.__pool, self.__dbname, self.__dsn)

    def __bool__(self):
        raise NotImplementedError()


def async_connection_info_for(db_or_uri, readonly=False):
    """Parse connection info for async connections."""
    # Reuse the existing connection_info_for logic from sql_db
    from odoo.sql_db import connection_info_for
    return connection_info_for(db_or_uri, readonly)


# Global async pools
_AsyncPool = None
_AsyncPool_readonly = None


async def async_db_connect(to, allow_uri=False, readonly=False):
    """Create an async database connection."""
    global _AsyncPool, _AsyncPool_readonly

    maxconn = config.get('db_maxconn_gevent', 64) if odoo.evented else config.get('db_maxconn', 64)

    if _AsyncPool is None and not readonly:
        _AsyncPool = AsyncConnectionPool(int(maxconn), readonly=False)
    if _AsyncPool_readonly is None and readonly:
        _AsyncPool_readonly = AsyncConnectionPool(int(maxconn), readonly=True)

    db, info = async_connection_info_for(to, readonly)
    if not allow_uri and db != to:
        raise ValueError('URI connections not allowed')

    return AsyncConnection(_AsyncPool_readonly if readonly else _AsyncPool, db, info)


async def close_async_pools():
    """Close all async connection pools."""
    global _AsyncPool, _AsyncPool_readonly

    if _AsyncPool:
        await _AsyncPool.close_all()
        _AsyncPool = None

    if _AsyncPool_readonly:
        await _AsyncPool_readonly.close_all()
        _AsyncPool_readonly = None
